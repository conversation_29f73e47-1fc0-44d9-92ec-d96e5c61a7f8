package com.wexl.retail.notifications.model;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum NotificationType {
  CLASSROOM,
  SECTION,
  INDIVIDUAL,
  G<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>AT<PERSON>,
  <PERSON><PERSON><PERSON>LAR,
  LEAVE_REQUEST,
  LEAVE_APPROVED,
  LEAVE_DISAPPROVED,
  APPOINTMENT_REQUEST,
  APPOINTMENT_APPROVED,
  APPOINTMENT_DISAPPROVED,
  EMAIL,
  MESSAGE,
  STAFF_APPOINTMENT,
  FORUM,
  GATEPASS_REQUEST,
  GATEPASS_APPROVED,
  GATEPASS_DISAPPROVED,
  INFIRMARY,
  HOLIDAY,
  CALENDER_EVENT
}
