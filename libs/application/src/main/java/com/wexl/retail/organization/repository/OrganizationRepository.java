package com.wexl.retail.organization.repository;

import com.wexl.retail.globalprofile.model.GlobalProfile;
import com.wexl.retail.model.User;
import com.wexl.retail.organization.dto.*;
import com.wexl.retail.organization.model.Organization;
import jakarta.transaction.Transactional;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface OrganizationRepository extends CrudRepository<Organization, Long> {
  @Query(value = "select * from orgs where slug = :orgSlug", nativeQuery = true)
  Organization findBySlug(String orgSlug);

  @Query(
      value = "select * from orgs where deleted_at is NULL order by created_at desc",
      nativeQuery = true)
  List<Organization> findAllOrgs();

  @Query(value = "select * from orgs where is_parent = 'true'", nativeQuery = true)
  List<Organization> getAllParents();

  @Query(
      value =
          """
                              select child.slug from orgs parent
                              inner join orgs child on parent.id=child.parent_id
                              where parent.slug=:parentOrgSlug
                              """,
      nativeQuery = true)
  List<String> getAllChildOrgSlugs(String parentOrgSlug);

  @Query(
      value =
          """
                              select parent.* from orgs parent
                              inner join orgs child on parent.id=child.parent_id
                              where child.slug=:childOrgSlug
                              """,
      nativeQuery = true)
  List<Organization> getParentOrgByChildOrgSlug(String childOrgSlug);

  @Query(
      value =
          """
                  select * from orgs where parent_id=:parentId
                  """,
      nativeQuery = true)
  List<Organization> getAllChildOrgsByParentId(Long parentId);

  @Query(value = "select * from orgs where is_publisher = 'true'", nativeQuery = true)
  List<Organization> getAllPubishers();

  @Query(
      value = "select * from orgs where is_parent =:isParent or is_publisher = :isPublisher",
      nativeQuery = true)
  List<Organization> getParentsPublishers(Boolean isParent, Boolean isPublisher);

  @Query(
      value = "SELECT *  FROM orgs  WHERE LOWER(abbreviation) = LOWER(:abbreviation)",
      nativeQuery = true)
  List<Organization> findByAbbreviationWithIgnoreCase(String abbreviation);

  @Query(
      value =
          """
                 select to_char(to_date(to_char(og.created_at , 'YYYY-MM-DD'),'YYYY-MM-DD'),'MONTH') as monthName  ,
                 count(og.created_at) as orgsCount
                 from orgs og where to_char(og.created_at , 'YYYY-MM-DD')  >= :fromDate
                 and to_char(og.created_at , 'YYYY-MM-DD')  <= :toDate
                 group by EXTRACT(YEAR from og.created_at),EXTRACT(Month from og.created_at), monthName
                 order by EXTRACT(YEAR from og.created_at) DESC, EXTRACT(Month from og.created_at) DESC""",
      nativeQuery = true)
  List<OrgsandUsersByMonthData> getOrgsbyMonth(LocalDate fromDate, LocalDate toDate);

  @Query(
      value =
          """
                select to_char(to_date(to_char(u.created_at , 'YYYY-MM-DD'),'YYYY-MM-DD'),'MONTH') as monthName ,
                count(u.created_at) as usersCount from users u
                where to_char(u.created_at , 'YYYY-MM-DD')  >= :fromDate
                and to_char(u.created_at , 'YYYY-MM-DD')  <= :toDate
                group by EXTRACT(YEAR from u.created_at),EXTRACT(Month from u.created_at), monthName
                order by EXTRACT(YEAR from u.created_at) DESC, EXTRACT(Month from u.created_at) DESC""",
      nativeQuery = true)
  List<OrgsandUsersByMonthData> getUsersbyMonth(LocalDate fromDate, LocalDate toDate);

  @Query(
      value =
          """
                      with cte as
                      SELECT  u.deleted_at ,count(*) as total
                      sum(CASE  WHEN r.name = 'ROLE_ISTUDENT' THEN 1 else 0 end ) as students
                      sum(CASE  WHEN r.name = 'ROLE_ITEACHER' THEN 1 else 0 end) as teachers
                      sum(CASE WHEN r.name not in  ('ROLE_ITEACHER','ROLE_ISTUDENT') THEN 1 else 0 end) as others
                      FROM users u
                      inner join public.user_roles ur on  ur.user_id = u.id
                      inner join public.roles  r on r.id  = ur.role_id
                      where to_char(u.created_at , 'yyyy-mm-dd') >= :date
                      group by  u.deleted_at )
                      select 'ACTIVE' as status , COALESCE(sum(total),0) as total ,COALESCE(sum(students),0) as students , COALESCE(sum(teachers),0) as teachers,COALESCE(sum(others),0) as others
                      from cte where deleted_at is  null
                      union
                      select 'INACTIVE' as status ,COALESCE(sum(total),0) as total ,COALESCE(sum(students),0) as students , COALESCE(sum(teachers),0) as teachers,COALESCE(sum(others),0) as others
                      from cte where deleted_at is not null
                      union
                      select 'TOTAL' as status , COALESCE(sum(total),0) as total ,COALESCE(sum(students),0) as students , COALESCE(sum(teachers),0) as teachers,COALESCE(sum(others),0) as others
                      from cte
                      order by status
                      """,
      nativeQuery = true)
  List<UserAnalytics> getUserAnalyticsByDate(LocalDate date);

  @Query(
      value =
          """
                      select og.name as nameOrgs
                      (select count(*) from users u where u.organization=og.slug) as usersCount
                      (select count(*) as mlpsCount from mlp m
                      where  m.org_slug = og.slug
                      as mlpsTriggered
                      (select count(*) as mlpsCount from mlp m
                      join mlp_inst mi on m.id=mi.mlp_id
                      where m.org_slug = og.slug and mi.attendance_percentage is not null  ) as mlpsAttendence
                      (count(u.id) ) as teachersCount
                      from orgs og
                      inner join users u on u.organization = og.slug
                      inner join user_roles ur on u.id=ur.user_id
                      inner join roles r on ur.role_id = r.id
                      where r.name='ROLE_ITEACHER' and u.deleted_at is null and to_char(og.created_at , 'YYYY-MM-DD')  >= :fromDate\s
                      and to_char(og.created_at , 'YYYY-MM-DD')  <= :toDate group by og.name ,og.slug limit :limit
                      """,
      nativeQuery = true)
  List<MlpAnalysisByInstituteData> getMlpAnalysisByInstituteData(
      LocalDate fromDate, LocalDate toDate, Integer limit);

  @Query(
      value =
          """
        select count(*) as OrgsCount,(select count(*) from orgs) as TotalOrgsCount from orgs og where to_char(og.created_at , 'YYYY-MM-DD') >= :date
        """,
      nativeQuery = true)
  OrganizationCount getOrganizationCount(LocalDate date);

  @Query(
      value =
          """
        select count(*) as UsersCount ,(select count(*) from users) as TotalUsersCount FROM users where to_char(created_at , 'yyyy-mm-dd') >= :date
         """,
      nativeQuery = true)
  UserCount getUsersCount(LocalDate date);

  List<Organization> findAllBySlugIn(List<String> slugs);

  @Query(
      value =
          """
                  select o.slug as orgSlug ,o.name as orgName , to_char(o.created_at,'yyyy-mm-dd') as date ,count(u.*) as Count from orgs o
                  inner join users u on u.organization = o.slug
                  where EXTRACT(MONTH from o.created_at)= :month
                  and  EXTRACT(year from o.created_at) = :year
                  group by o.slug,o.name ,o.created_at order by count desc
                  """,
      nativeQuery = true)
  List<MetricsCountByOrg> getOrgWiseUserStatsByMonth(int month, int year);

  @Query(value = "select * from orgs where slug in (:orgSlug)", nativeQuery = true)
  List<Organization> findBySlug(List<String> orgSlug);

  List<Organization> findAllByCreatedByOrderByCreatedAtDesc(User user);

  @Query(
      value = "select * from orgs where slug not in (:orgSlug) order by created_at desc",
      nativeQuery = true)
  List<Organization> findOrgsForMlpAttendance(List<String> orgSlug);

  List<Organization> findAllByCreatedByAndDeletedAtIsNull(User user);

  List<Organization> findAllByDeletedAtIsNull();

  List<Organization> findAllByProfile(GlobalProfile profile);

  List<Organization> findByProfileAndDeletedAtIsNull(GlobalProfile globalProfile);

  @Query(
      value = "select * from orgs o where parent_id = :parentId and deleted_at is null",
      nativeQuery = true)
  List<Organization> findAllByParentIdAndDeletedAtIsNull(Long parentId);

  @Query(
      value = "select * from orgs o where \"attributes\" ->> 'student_passcode'=:passcode",
      nativeQuery = true)
  Optional<Organization> findByStudentPasscode(String passcode);

  List<Organization> findByParentAndDeletedAtIsNull(Organization parentOrg);

  @Query(
      value =
          """
                          select o."name" organization , count(u.id) totalCount, count(lh.user_id) loggedInCount, count(u.id) - count(lh.user_id) notLoggedInCount
                          from users u
                          left join (select distinct user_id from login_history lh where to_char( created_at,'YYYY-MM-DD') >= :fromDate and to_char( created_at,'YYYY-MM-DD') <= :toDate ) lh
                          on lh.user_id = u.id
                          join teacher_details td on td.user_id = u.id
                          join orgs o on o.slug = u.organization and o.deleted_at is null
                          where o.slug in (:orgSlugs) and u.deleted_at is null
                          group by o."name"
                          """,
      nativeQuery = true)
  List<LoginDetails> getTeacherLoginDetails(List<String> orgSlugs, String fromDate, String toDate);

  @Query(
      value =
          """
                          select o."name" organization , count(u.id) totalCount, count(lh.user_id) loggedInCount, count(u.id) - count(lh.user_id) notLoggedInCount
                          from users u
                          left join (select distinct user_id from login_history lh where to_char( created_at,'YYYY-MM-DD') >= :fromDate and to_char( created_at,'YYYY-MM-DD') <= :toDate ) lh
                          on lh.user_id = u.id
                          join students s on s.user_id = u.id
                          join orgs o on o.slug = u.organization
                          where o.slug in (:orgSlugs) and u.deleted_at is null and o.deleted_at is null
                          group by o."name"
                          """,
      nativeQuery = true)
  List<LoginDetails> getStudentLoginDetails(List<String> orgSlugs, String fromDate, String toDate);

  @Query(
      value =
          """
                  SELECT
                      o.name AS organization,
                      COUNT(DISTINCT m.id) AS mlpCount,
                      COALESCE(SUM(m.attendance_percentage), 0) / NULLIF(COUNT(m.id), 0) AS mlpAttendancePercentage
                  FROM
                      orgs o
                  LEFT JOIN
                      mlp m
                  ON
                      o.slug = m.org_slug
                      AND (:fromDate IS NULL OR to_char(m.created_at, 'yyyy-MM-dd') >= :fromDate)
                      AND (:toDate IS NULL OR to_char(m.created_at, 'yyyy-MM-dd') <= :toDate)
                  LEFT JOIN
                      mlp_inst mi
                  ON
                      mi.mlp_id = m.id
                  WHERE
                      o.parent_id = :parentId
                      AND o.deleted_at IS NULL
                  GROUP BY
                      o.name;


                          """,
      nativeQuery = true)
  List<CountsByParentOrg> getMlpCountByOrg(Long parentId, String fromDate, String toDate);

  @Query(
      value =
          """
                  SELECT
                      o.name AS organization,
                      COUNT(CASE WHEN td."type" IN ('SCHOOL_TEST', 'MOCK_TEST', 'LIVE_WORKSHEET', 'WORKSHEET') THEN 1 ELSE NULL END) AS testsCount,
                      COUNT(CASE WHEN td."type" = 'ASSIGNMENT' THEN 1 ELSE NULL END) AS assignmentCount
                  FROM orgs o
                  LEFT JOIN test_definitions td ON o.slug = td.organization  AND td.deleted_at IS NULL
                  AND (:fromDate IS NULL OR to_char(td.created_at, 'yyyy-MM-dd') >= :fromDate)
                  AND (:toDate IS NULL OR to_char(td.created_at, 'yyyy-MM-dd') <= :toDate)
                  WHERE o.parent_id = :parentId AND o.deleted_at IS NULL
                  GROUP BY o.name;

                          """,
      nativeQuery = true)
  List<CountsByParentOrg> getTestByParentOrg(Long parentId, String fromDate, String toDate);

  @Query(
      value =
          """
                          select u.first_name as fullName,u.auth_user_id as authId,o."name" as organization,s."name" as sectionName,
                          s."uuid" as sectionUuid,td.institute_name as instituteName,s.grade_name as gradeName,s.grade_slug as gradeSlug from orgs o
                          join users u on o.slug = u.organization
                          join teacher_details td on td.user_id  = u.id
                          join sections s on s.teacher_id = td.id
                          where o.slug = :orgSlug
                          """,
      nativeQuery = true)
  List<TeachersByOrg> getTeachersByOrgSlug(String orgSlug);

  @Query(
      value =
          """

                          select purge_student(u.auth_user_id)
                        from users u
                        join students st on st.user_id = u.id
                        where u.organization = (:orgSlug)
                        """,
      nativeQuery = true)
  List<String> deleteStudentsByOrgSlug(String orgSlug);

  @Modifying
  @Transactional
  @Query(
      value =
          """

                          update user_roles set role_id = 5 where user_id in
                                (select u.id
                                from users u
                                join teacher_details td on td.user_id = u.id
                                where u.organization = :orgSlug)
                        """,
      nativeQuery = true)
  void updateAdminRole(String orgSlug);

  @Transactional
  @Query(
      value =
          """

                          select purge_teacher(auth_user_id, organization)
                                from users u
                                join teacher_details td on td.user_id = u.id
                                where u.organization = (:orgSlug)
                        """,
      nativeQuery = true)
  List<String> deleteTeacherByOrgSlug(String orgSlug);

  @Transactional
  @Query(
      value =
          """
                        select purge_organization(slug) from orgs where slug = :orgSlug
                        """,
      nativeQuery = true)
  List<String> deleteOrganizationBySlug(String orgSlug);
}
