package com.wexl.retail.organization.auth;

import com.wexl.retail.auth.AuthResponse;
import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.content.model.OrganizationRequest;
import com.wexl.retail.curriculum.service.CurriculumService;
import com.wexl.retail.curriculum.service.OrgSettingsService;
import com.wexl.retail.email.EmailService;
import com.wexl.retail.generic.GenericResponse;
import com.wexl.retail.globalprofile.model.AppTemplate;
import com.wexl.retail.globalprofile.model.GlobalProfile;
import com.wexl.retail.globalprofile.repository.GlobalProfileRepository;
import com.wexl.retail.globalprofile.repository.RoleTemplateRepository;
import com.wexl.retail.globalprofile.service.RoleTemplateService;
import com.wexl.retail.idp.UserIdpService;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.model.*;
import com.wexl.retail.organization.dto.*;
import com.wexl.retail.organization.handler.EntityHandler;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.otp.OtpResponse;
import com.wexl.retail.otp.OtpServiceLegacy;
import com.wexl.retail.repository.AddressRepository;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.student.subject.profiles.service.SubjectProfileMigrationService;
import com.wexl.retail.teacher.auth.TeacherAuthService;
import com.wexl.retail.teacher.auth.TeacherSignupRequest;
import com.wexl.retail.teacher.orgs.TeacherOrgsService;
import com.wexl.retail.test.schedule.repository.ScheduleTestRepository;
import com.wexl.retail.util.AccessToken;
import com.wexl.retail.util.Constants;
import com.wexl.retail.util.ReCaptchaService;
import com.wexl.retail.util.ValidationUtils;
import jakarta.validation.Valid;
import java.time.LocalDate;
import java.util.*;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Random;
import java.util.stream.IntStream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class OrganizationAuthService {

  public static final String INDIA = "IN";
  public static final String ORG_TYPE_SLUG = "school-direct";
  private static final String BEARER = "Bearer ";
  private static final Random random = new Random();

  private static final String ADMIN_LAST_NAME = "ADMIN";
  @Autowired AuthService authService;
  @Autowired UserRepository userRepository;
  @Autowired private UserIdpService userIdpService;
  @Autowired EmailService emailService;
  @Autowired OtpServiceLegacy otpServiceLegacy;
  @Autowired public AddressRepository addressRepository;
  @Autowired ContentService contentService;
  @Autowired AccessToken accessToken;
  @Autowired ReCaptchaService reCaptchaService;
  @Autowired TeacherRepository teacherRepository;
  @Autowired TeacherAuthService teacherAuthService;
  @Autowired OrganizationRepository organizationRepository;
  @Autowired OrgSettingsService orgSettingsService;
  @Autowired CurriculumService curriculumService;
  private final GlobalProfileRepository globalProfileRepository;
  private final RoleTemplateService roleTemplateService;
  private final RoleTemplateRepository roleTemplateRepository;
  private final StudentRepository studentRepository;
  private final ScheduleTestRepository scheduleTestRepository;
  private final DateTimeUtil dateTimeUtil;
  private final TeacherOrgsService teacherOrgsService;

  @Autowired SubjectProfileMigrationService subjectProfileMigrationService;
  @Autowired List<EntityHandler<Organization>> organizationHandlers;
  @Autowired ValidationUtils validationUtils;

  public ResponseEntity<Object> createOrganization(
      Boolean requestComingFromMobileApp,
      @Valid OrganizationSignupRequest organizationSignupRequest) {
    try {
      validateEmail(organizationSignupRequest.getEmail());

      if (Objects.nonNull(organizationSignupRequest.getEmailOtp().getOtp())
          && !otpServiceLegacy.verifyOtp(
              organizationSignupRequest.getEmailOtp().getOtp(),
              organizationSignupRequest.getEmailOtp().getOtpId())) {
        throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidEmailOTP");
      }
      if (Objects.nonNull(organizationSignupRequest.getMobileOtp().getOtp())
          && !otpServiceLegacy.verifyOtp(
              organizationSignupRequest.getMobileOtp().getOtp(),
              organizationSignupRequest.getMobileOtp().getOtpId())) {
        throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidMobileOTP");
      }
      var response = createOrgInContentAndRetail(organizationSignupRequest, Boolean.FALSE);
      organizationHandlers.forEach(
          organizationEntityHandler -> organizationEntityHandler.postSave(response.organization()));
      // Return Access Token
      String jwtToken =
          authService.generateOrgAdminAccessToken(
              requestComingFromMobileApp, response.user(), LoginMethod.SYSTEM_CREATED);
      return ResponseEntity.status(HttpStatus.OK)
          .body(AuthResponse.builder().accessToken(jwtToken).build());
    } catch (Exception ex) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, ex.getMessage(), ex);
    }
  }

  public OrganizationDto.SignupResponse createOrgInContentAndRetail(
      OrganizationSignupRequest organizationSignupRequest, Boolean isAmbassdorSignup) {
    // Create Organization
    OrganizationRequest orgRequest;
    orgRequest = createOrganizationInContent(organizationSignupRequest);

    // Create Organization in retail
    final Organization organization;
    organization =
        createOrganizationInRetail(orgRequest, organizationSignupRequest, isAmbassdorSignup);

    // Create User In DB and Cognito

    if (Objects.isNull(organizationSignupRequest.getFirstName())) {
      organizationSignupRequest.setFirstName(organizationSignupRequest.getOrganizationName());
    }

    if (Objects.isNull(organizationSignupRequest.getLastName())) {
      organizationSignupRequest.setLastName(ADMIN_LAST_NAME);
    }

    TeacherSignupRequest teacherSignUpRequest;
    teacherSignUpRequest =
        teacherSignupRequestMapper(organizationSignupRequest, orgRequest.getSlug());

    teacherAuthService.createTeacherInBulk(teacherSignUpRequest);

    Optional<User> user;
    user = userRepository.findUserByEmail(teacherSignUpRequest.getEmailAddress());

    if (user.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.OrgAdminCreationFailed");
    }
    return OrganizationDto.SignupResponse.builder()
        .organization(organization)
        .user(user.get())
        .build();
  }

  public void validateEmail(String email) {
    var isExistEmail = authService.findFirstMatchingEmail(email);
    if (isExistEmail != null) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Email.Registered");
    }
  }

  private TeacherSignupRequest teacherSignupRequestMapper(
      OrganizationSignupRequest signupRequest, String orgSlug) {
    GlobalProfile globalProfile =
        globalProfileRepository
            .findById(signupRequest.getProfileId())
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.INVALID_REQUEST, "error.GlobalProfileNotFound"));
    var roleTemplates =
        roleTemplateRepository.findByGlobalProfileAndTemplate(globalProfile, AppTemplate.ADMIN);
    if (roleTemplates.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.RoleTemplateNotFound");
    }
    return TeacherSignupRequest.builder()
        .emailAddress(signupRequest.getEmail())
        .firstName(signupRequest.getFirstName())
        .lastName(signupRequest.getLastName())
        .mobileNumber(signupRequest.getMobileNumber())
        .password(signupRequest.getPassword())
        .termsAndConditions(signupRequest.getTermsAndConditions())
        .roleTemplate(roleTemplates.getFirst())
        .orgSlug(orgSlug)
        .orgAdmin(true)
        .build();
  }

  private OrganizationRequest createOrganizationInContent(
      OrganizationSignupRequest organizationSignupRequest) {
    var orgSlug = generateUrlSlug(organizationSignupRequest.getOrganizationName());
    OrganizationRequest orgRequest =
        orgRequestTransformerForContent(organizationSignupRequest, orgSlug);
    String tempToken =
        BEARER + accessToken.generateTokenByRole(UserRole.ROLE_ORG_ADMIN, orgSlug, authService);

    contentService.createOrganization(tempToken, orgRequest);
    return orgRequest;
  }

  private Organization createOrganizationInRetail(
      OrganizationRequest organizationRequest,
      OrganizationSignupRequest organizationSignupRequest,
      Boolean isAmbassdorSignup) {
    Organization organization = new Organization();
    orgRequestToOrgs(organization, organizationRequest);
    if (Boolean.FALSE.equals(isAmbassdorSignup)) {
      User user = authService.getUserDetails();
      organization.setCreatedBy(user);
    }
    organization.setIsParent(organizationSignupRequest.isParent());
    organization.setIsPublisher(organizationSignupRequest.isPublisher());
    organization.setAttributes(getOrganizationAttribute());
    if (organizationSignupRequest.getParentOrg() != null) {
      organization.setParent(
          organizationRepository.findBySlug(organizationSignupRequest.getParentOrg()));
    }
    if (organizationSignupRequest.getPublisherSlug() != null) {
      organization.setPublisher(
          organizationRepository.findBySlug(organizationSignupRequest.getPublisherSlug()));
    }

    final Optional<GlobalProfile> possibleGlobalProfile =
        globalProfileRepository.findById(organizationSignupRequest.getProfileId());
    if (possibleGlobalProfile.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.invalidInput");
    }
    organization.setProfile(possibleGlobalProfile.get());
    organization.setSelfSignup(organizationSignupRequest.isSelfSignup());
    var uiConfig = organizationRequest.getUiConfig();
    if (uiConfig != null) {
      organization.setSettingsMigration(Boolean.TRUE);
      organization.setLogo(uiConfig.getLogo());
      organization.setTheme(uiConfig.getTheme());
      organization.setWebsite(uiConfig.getWebsite());
      organization.setLogoutUrl(uiConfig.getLogoutUrl());
      organization.setMobileLogo(uiConfig.getMobileLogo());
      organization.setOrgProfile(uiConfig.getOrgProfile());
      organization.setStudentUrl(uiConfig.getStudentUrl());
      organization.setSendSms(uiConfig.getSendSms());
    }
    organizationRepository.save(organization);

    try {
      subjectProfileMigrationService.createStandardSubjectProfiles(organization.getSlug());
    } catch (ApiException e) {
      log.error(
          "Failed to create standard subject profiles for organization ["
              + organization.getSlug()
              + "]");
    }
    return organization;
  }

  private Attributes getOrganizationAttribute() {
    return Attributes.builder()
        .teacherPasscode(getPasscode())
        .studentPasscode(getPasscode())
        .build();
  }

  public Organization orgRequestToOrgs(
      Organization organization, OrganizationRequest organizationRequest) {
    organization.setName(organizationRequest.getName());
    organization.setSlug(organizationRequest.getSlug());
    organization.setCurriculum(organizationRequest.getCurriculum());
    organization.setMetadata(organizationRequest.getOrgMetaData());
    organization.setAbbreviation(organizationRequest.getAbbreviation());
    return organization;
  }

  public OrganizationRequest orgRequestTransformerForContent(
      OrganizationSignupRequest organizationSignupRequest, String orgSlug) {
    OrganizationRequest orgRequest = new OrganizationRequest();
    orgRequest.setName(organizationSignupRequest.getOrganizationName());
    orgRequest.setType(ORG_TYPE_SLUG);
    orgRequest.setSlug(orgSlug);
    orgRequest.setOrgTypeSlug(ORG_TYPE_SLUG);
    orgRequest.setPreferredBoardSlug(organizationSignupRequest.getOrganizationBoard());
    orgRequest.setUiConfig(organizationSignupRequest.getUiConfig());
    orgRequest.setOrgMetaData(
        OrgMetaData.builder()
            .branchName(organizationSignupRequest.getBranchName())
            .branchStrength(organizationSignupRequest.getBranchStrength())
            .principalName(organizationSignupRequest.getPrincipalName())
            .build());
    if (organizationSignupRequest.getBoards() != null) {
      orgRequest.setCurriculum(
          orgSettingsService.transformBoards(organizationSignupRequest.getBoards()));
    }
    orgRequest.setParentOrgSlug(organizationSignupRequest.getParentOrg());
    return orgRequest;
  }

  public OtpResponse sendOtp(String captchaCode, String target, String reference) {

    verifyCaptcha(captchaCode);
    return otpServiceLegacy.sendOtpToTarget(target, reference);
  }

  public GenericResponse verifyOtp(OtpVerificationRequest verificationRequest) {

    boolean otpStatus =
        otpServiceLegacy.verifyOtp(verificationRequest.getOtp(), verificationRequest.getOtpId());
    if (otpStatus) {
      return GenericResponse.builder().data(otpStatus).build();
    } else {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.OTP");
    }
  }

  private String generateUrlSlug(String name) {
    if (name.length() < 3) {
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "error.InstituteName.Characters");
    }
    return name.replaceAll("[^A-Za-z0-9]+", "").toLowerCase().substring(0, 3) + getRandomNumber(6);
  }

  public String getRandomNumber(int digCount) {
    StringBuilder sb = new StringBuilder(digCount);
    IntStream.range(0, digCount).forEach(s -> sb.append(random.nextInt(10)));
    return String.valueOf(sb);
  }

  private Long getPasscode() {
    var passcode = Long.parseLong(getRandomNumber(6));
    if (StringUtils.length(String.valueOf(passcode)) == 6) {
      return passcode;
    }
    return getPasscode();
  }

  public void verifyCaptcha(String captchaCode) {
    if (captchaCode == null) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.CaptchaCode.null");
    }

    if (!reCaptchaService.verify(captchaCode)) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.ReCaptcha.Invalid");
    }
  }

  public OrganizationResponse getOrganization(String orgSlug) {
    try {
      User orgAdmin = userRepository.getOrgAdminForOrganization(orgSlug);
      Organization organization = organizationRepository.findBySlug(orgSlug);
      if (!Boolean.TRUE.equals(organization.getSettingsMigration())) {
        orgSettingsService.getUiConfigByOrgSlug(orgSlug);
      }
      return OrganizationResponse.builder()
          .email(orgAdmin.getEmail())
          .mobileNumber(orgAdmin.getMobileNumber())
          .name(organization.getName())
          .slug(organization.getSlug())
          .profileId(organization.getProfile() != null ? organization.getProfile().getId() : null)
          .profileName(
              organization.getProfile() != null ? organization.getProfile().getName() : null)
          .isParent(organization.getIsParent())
          .isPublisher(organization.getIsPublisher())
          .metadata(organization.getMetadata())
          .parent(
              organization.getParent() != null
                  ? buildParentResponse(organization.getParent())
                  : null)
          .publisher(
              organization.getPublisher() != null
                  ? buildPublisherResponse(organization.getPublisher())
                  : null)
          .eduBoardList(curriculumService.getBoardsHierarchy(orgSlug))
          .isSelfSignup(organization.getSelfSignup())
          .abbreviation(organization.getAbbreviation())
          .logo(organization.getLogo())
          .mobileLogo(organization.getMobileLogo())
          .theme(organization.getTheme())
          .logoutUrl(organization.getLogoutUrl())
          .studentUrl(organization.getStudentUrl())
          .orgProfile(organization.getOrgProfile())
          .website(organization.getWebsite())
          .sendSms(organization.getSendSms() == null ? Boolean.FALSE : organization.getSendSms())
          .build();
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.NO_RECORD_FOUND, "error.OrganizationNotFound ");
    }
  }

  public OrgParentAndPublisherResponse.ParentResponse buildParentResponse(Organization parentOrg) {
    return OrgParentAndPublisherResponse.ParentResponse.builder()
        .name(parentOrg.getName())
        .slug(parentOrg.getSlug())
        .createdAt(parentOrg.getCreatedAt())
        .build();
  }

  public OrgParentAndPublisherResponse.PublisherResponse buildPublisherResponse(
      Organization publisherOrg) {
    return OrgParentAndPublisherResponse.PublisherResponse.builder()
        .name(publisherOrg.getName())
        .slug(publisherOrg.getSlug())
        .createdAt(publisherOrg.getCreatedAt())
        .build();
  }

  public OrganizationResponse editOrganization(
      String orgSlug, OrganizationSignupRequest organizationSignupRequest) {

    Organization organization = organizationRepository.findBySlug(orgSlug);
    if (Objects.isNull(organization)) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.OrganizationNotFound");
    }
    OrganizationRequest organizationRequest =
        updateOrganizationInContent(organizationSignupRequest, orgSlug);
    updateOrgsInRetail(organization, organizationRequest, organizationSignupRequest);
    return getOrganization(orgSlug);
  }

  private OrganizationRequest updateOrganizationInContent(
      OrganizationSignupRequest organizationSignupRequest, String orgSlug) {
    OrganizationRequest orgRequest =
        orgRequestTransformerForContent(organizationSignupRequest, orgSlug);
    String tempToken =
        BEARER
            + accessToken.generateTokenByRole(
                UserRole.ROLE_ORG_ADMIN, orgRequest.getSlug(), authService);
    try {
      contentService.editOrganization(tempToken, orgRequest);
    } catch (Exception e) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.OrganizationEdit.Failed", e);
    }
    return orgRequest;
  }

  private void updateOrgsInRetail(
      Organization organization,
      OrganizationRequest organizationRequest,
      OrganizationSignupRequest organizationSignupRequest) {

    User orgAdmin = userRepository.getOrgAdminForOrganization(organization.getSlug());
    orgAdmin.setMobileNumber(organizationSignupRequest.getMobileNumber());
    if (!organizationSignupRequest.getPassword().isEmpty()) {
      userIdpService.adminSetUserPassword(
          orgAdmin.getAuthUserId(), organizationSignupRequest.getPassword());
    }
    orgRequestToOrgs(organization, organizationRequest);
    if (!organizationSignupRequest.isParent()) {
      organization.setParent(
          organizationRepository.findBySlug(organizationSignupRequest.getParentOrg()));
    }
    var globalProfile =
        globalProfileRepository
            .findById(organizationSignupRequest.getProfileId())
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.INVALID_REQUEST, "error.InvalidGlobalProfile"));
    if (!organization.getProfile().getId().equals(globalProfile.getId())) {
      roleTemplateService.updateProfileTemplates(globalProfile, organization);
    }
    var uiConfig = organizationRequest.getUiConfig();
    organization.setProfile(globalProfile);
    organization.setIsParent(organizationSignupRequest.isParent());
    organization.setSelfSignup(organizationSignupRequest.isSelfSignup());
    organization.setLogo(uiConfig.getLogo());
    organization.setWebsite(uiConfig.getWebsite());
    organization.setTheme(uiConfig.getTheme());
    organization.setOrgProfile(uiConfig.getOrgProfile());
    organization.setMobileLogo(uiConfig.getMobileLogo());
    organization.setStudentUrl(uiConfig.getStudentUrl());
    organization.setLogoutUrl(uiConfig.getLogoutUrl());
    organization.setSendSms(uiConfig.getSendSms());
    organizationRepository.save(organization);
    try {
      subjectProfileMigrationService.createStandardSubjectProfiles(organization.getSlug());
    } catch (ApiException e) {
      log.error(
          "Failed to create standard subject profiles for organization ["
              + organization.getSlug()
              + "]");
    }
  }

  public UserAnalyticsResponse getUserAnalytics(int timePeriod) {

    var data =
        organizationRepository.getUserAnalyticsByDate(LocalDate.now().minusMonths(timePeriod));

    return UserAnalyticsResponse.builder()
        .active(data.getFirst())
        .inactive(data.get(1))
        .total(data.get(2))
        .activeOtherUsersPercentage(
            Double.parseDouble(
                Constants.DECIMAL_FORMAT.format(
                    data.getFirst().getOthers() / data.getFirst().getTotal() * 100)))
        .activeStudentsPercentage(
            Double.parseDouble(
                Constants.DECIMAL_FORMAT.format(
                    data.getFirst().getStudents() / data.getFirst().getTotal() * 100)))
        .activeTeachersPercentage(
            Double.parseDouble(
                Constants.DECIMAL_FORMAT.format(
                    data.getFirst().getTeachers() / data.getFirst().getTotal() * 100)))
        .build();
  }

  public OrgsandUsersbyMonthResponse getOrganizationDetails(int timePeriod) {
    var orgData =
        organizationRepository.getOrganizationCount(LocalDate.now().minusMonths(timePeriod));
    var userData = organizationRepository.getUsersCount(LocalDate.now().minusMonths(timePeriod));

    return OrgsandUsersbyMonthResponse.builder()
        .orgsCount(orgData.getOrgsCount())
        .totalOrgsCount(orgData.getTotalOrgsCount())
        .usersCount(userData.getUsersCount())
        .totalUsersCount(userData.getTotalUsersCount())
        .build();
  }

  public List<OrganizationResponse> getAllOrgs() {

    List<Organization> organization = organizationRepository.findAllOrgs();
    return organization.stream()
        .filter(org -> !Constants.WEXL_INTERNAL.equals(org.getSlug()))
        .map(
            organization1 ->
                OrganizationResponse.builder()
                    .name(organization1.getName())
                    .slug(organization1.getSlug())
                    .isParent(organization1.getIsParent())
                    .isPublisher(organization1.getIsPublisher())
                    .metadata(organization1.getMetadata())
                    .parent(
                        organization1.getParent() != null
                            ? buildParentResponse(organization1.getParent())
                            : null)
                    .publisher(
                        organization1.getPublisher() != null
                            ? buildPublisherResponse(organization1.getPublisher())
                            : null)
                    .abbreviation(organization1.getAbbreviation())
                    .displayName(constructDisplayName(organization1))
                    .build())
        .sorted(Comparator.comparing(OrganizationResponse::getName))
        .toList();
  }

  private String constructDisplayName(Organization organization) {
    return Objects.nonNull(organization.getMetadata())
        ? organization.getName() + " - " + organization.getMetadata().getBranchName()
        : organization.getName();
  }

  public OrganizationDto.Response getOrgSummaries(String orgSlug) {
    var organization = validationUtils.isOrgValid(orgSlug);
    return OrganizationDto.Response.builder()
        .orgName(organization.getName())
        .noOfStudents(studentRepository.getStudentCountByOrg(orgSlug))
        .noOfTeachers(teacherRepository.getTeacherCountByOrg(orgSlug))
        .mlpConducted(scheduleTestRepository.getMlpCountByOrg(orgSlug))
        .onlineTestConducted(scheduleTestRepository.getOnlineTestCountByOrg(orgSlug))
        .zeroDigitalConducted(scheduleTestRepository.getDacCountByOrg(orgSlug))
        .build();
  }

  public List<GenericMetricResponse> getTeacherLoginDetails(
      String org, String fromDate, String toDate) {

    List<GenericMetricResponse> genericMetricResponses = new ArrayList<>();
    var convertedFromDate = dateTimeUtil.repositoryFriendlyDateFromEpoch(Long.valueOf(fromDate));
    var convertedToDate = dateTimeUtil.repositoryFriendlyDateFromEpoch(Long.valueOf(toDate));
    User user = authService.getUserDetails();
    List<Organization> teacherOrgs = teacherOrgsService.getChildOrgs(user.getAuthUserId());
    var teacherLoginDetails =
        organizationRepository.getTeacherLoginDetails(
            teacherOrgs.stream().map(Organization::getSlug).toList(),
            convertedFromDate,
            convertedToDate);
    for (LoginDetails teacherLogins : teacherLoginDetails) {
      Map<String, Object> data = new HashMap<>();
      data.put("organization", teacherLogins.getOrganization());
      data.put("logged_in_count", teacherLogins.getLoggedInCount());
      data.put("not_logged_in_count", teacherLogins.getNotLoggedInCount());
      data.put("total_count", teacherLogins.getTotalCount());
      genericMetricResponses.add(GenericMetricResponse.builder().data(data).build());
    }
    return genericMetricResponses;
  }

  public List<GenericMetricResponse> getStudentLoginDetails(
      String org, String fromDate, String toDate) {

    List<GenericMetricResponse> genericMetricResponses = new ArrayList<>();
    User user = authService.getUserDetails();
    List<Organization> teacherOrgs = teacherOrgsService.getChildOrgs(user.getAuthUserId());
    List<String> teacherOrgSlugs = teacherOrgs.stream().map(Organization::getSlug).toList();
    var convertedFromDate = dateTimeUtil.repositoryFriendlyDateFromEpoch(Long.valueOf(fromDate));
    var convertedToDate = dateTimeUtil.repositoryFriendlyDateFromEpoch(Long.valueOf(toDate));
    var studentLoginDetails =
        organizationRepository.getStudentLoginDetails(
            teacherOrgSlugs, convertedFromDate, convertedToDate);
    for (LoginDetails studentLogins : studentLoginDetails) {
      Map<String, Object> data = new HashMap<>();
      data.put("organization", studentLogins.getOrganization());
      data.put("logged_in_count", studentLogins.getLoggedInCount());
      data.put("not_logged_in_count", studentLogins.getNotLoggedInCount());
      data.put("total_count", studentLogins.getTotalCount());
      genericMetricResponses.add(GenericMetricResponse.builder().data(data).build());
    }
    return genericMetricResponses;
  }

  public List<GenericMetricResponse> getManagerOrgLevelMetrics(
      String org, String fromDate, String toDate) {
    var parentOrgId = organizationRepository.findBySlug(org);
    var convertedFromDate = dateTimeUtil.repositoryFriendlyDateFromEpoch(Long.valueOf(fromDate));
    var convertedToDate = dateTimeUtil.repositoryFriendlyDateFromEpoch(Long.valueOf(toDate));
    var mlpCountsAndPercentage =
        organizationRepository.getMlpCountByOrg(
            parentOrgId.getId(), convertedFromDate, convertedToDate);
    var onlineTestCountsAndPercentage =
        organizationRepository.getTestByParentOrg(
            parentOrgId.getId(), convertedFromDate, convertedToDate);
    var childOrgs = organizationRepository.findAllByParentIdAndDeletedAtIsNull(parentOrgId.getId());
    var teacherQuestionCount =
        contentService.getQuestionsCount(
            org,
            childOrgs.stream().map(Organization::getSlug).toList(),
            convertedFromDate,
            convertedToDate);
    List<GenericMetricResponse> genericMetricResponses = new ArrayList<>();
    Map<String, Integer> questionsCount = new HashMap<>();
    Map<String, Integer> mlpCountsMap = new HashMap<>();
    Map<String, CountsByParentOrg> onlineTestCountsAndPercentageMap = new HashMap<>();
    Map<String, Float> mlpAttendancePercentageMap = new HashMap<>();
    for (QuestionCountByOrg orgQuestionCounts : teacherQuestionCount) {
      questionsCount.put(orgQuestionCounts.getOrganization(), orgQuestionCounts.getCount());
    }
    for (CountsByParentOrg mlpCounts : mlpCountsAndPercentage) {
      mlpCountsMap.put(mlpCounts.getOrganization(), mlpCounts.getMlpCount());
      mlpAttendancePercentageMap.put(
          mlpCounts.getOrganization(),
          mlpCounts.getMlpAttendancePercentage() != null
              ? (float) (Math.round(mlpCounts.getMlpAttendancePercentage() * 100.0) / 100.0)
              : 0.0f);
    }
    onlineTestCountsAndPercentage.forEach(
        test -> onlineTestCountsAndPercentageMap.put(test.getOrganization(), test));
    for (Organization organization : childOrgs) {
      Map<String, Object> data = new HashMap<>();
      Map<String, Object> summary = new HashMap<>();

      data.put("organization", organization.getName());
      summary.put(
          "test_count",
          onlineTestCountsAndPercentageMap.get(organization.getName()).getTestsCount());
      summary.put(
          "assignment_count",
          onlineTestCountsAndPercentageMap.get(organization.getName()).getAssignmentCount());
      summary.put("mlp_counts", mlpCountsMap.get(organization.getName()));
      summary.put(
          "mlp_attendance_percentage", mlpAttendancePercentageMap.get(organization.getName()));
      summary.put(
          "questions_count_by_org",
          questionsCount.get(organization.getName()) != null
              ? questionsCount.get(organization.getName())
              : 0);
      genericMetricResponses.add(
          GenericMetricResponse.builder().data(data).summary(summary).build());
    }
    return genericMetricResponses;
  }
}
